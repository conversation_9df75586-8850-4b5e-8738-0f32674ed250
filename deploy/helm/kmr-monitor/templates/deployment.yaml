{{- range .Values.StateMetrics.deployments }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/component: exporter
    app.kubernetes.io/name: {{ .name }}
    app.kubernetes.io/version: {{ $.Values.StateMetrics.version }}
  name: {{ .name }}
  namespace: kmr-monitor
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ .name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/component: exporter
        app.kubernetes.io/name: {{ .name }}
        app.kubernetes.io/version: {{ $.Values.StateMetrics.version }}
    spec:
      automountServiceAccountToken: true
      containers:
        - args:
{{- if .args }}
{{- range .args }}
            - {{ . }}
{{- end }}
{{- else }}
            - --resources=certificatesigningrequests,configmaps,cronjobs,daemonsets,deployments,endpoints,horizontalpodautoscalers,ingresses,jobs,leases,limitranges,mutatingwebhookconfigurations,namespaces,networkpolicies,nodes,persistentvolumeclaims,persistentvolumes,poddisruptionbudgets,replicasets,replicationcontrollers,resourcequotas,secrets,services,statefulsets,storageclasses,validatingwebhookconfigurations,volumeattachments
{{- end }}
            - --metric-labels-allowlist=pods=[*],namespaces=[*],nodes=[*],services=[*],jobs=[*]
            - --metric-annotations-allowlist=pods=[*],namespaces=[*],nodes=[*],services=[*],jobs=[*]
          image: "{{ $.Values.StateMetrics.imageRepositoryHost}}/{{ $.Values.StateMetrics.repository }}:{{ $.Values.StateMetrics.tag }}"
          livenessProbe:
            httpGet:
              path: /livez
              port: http-metrics
            initialDelaySeconds: 5
            timeoutSeconds: 5
          name: kmr-kube-state-metrics
          ports:
            - containerPort: {{ $.Values.StateMetrics.httpPort }}
              name: http-metrics
            - containerPort: {{ $.Values.StateMetrics.telemetryPort }}
              name: telemetry
          readinessProbe:
            httpGet:
              path: /readyz
              port: telemetry
            initialDelaySeconds: 5
            timeoutSeconds: 5
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - ALL
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            runAsUser: 65534
            seccompProfile:
              type: RuntimeDefault
          resources:
            limits:
              cpu: {{ .resources.limits.cpu | default "100m" }}
              memory: {{ .resources.limits.memory | default "500Mi" }}
            requests:
              cpu: {{ .resources.requests.cpu | default "10m" }}
              memory: {{ .resources.requests.memory | default "50Mi" }}
      nodeSelector:
{{- if .nodeSelector }}
{{ toYaml .nodeSelector | nindent 8 }}
{{- else }}
        dedicated: serverless-operator
{{- end }}
        {{ toYaml $.Values.StateMetrics.pod.nodeSelector | nindent 8}}
      tolerations:
        {{ toYaml $.Values.StateMetrics.pod.tolerations | nindent 8 }}
      serviceAccountName: kmr-kube-state-metrics
{{- end }}