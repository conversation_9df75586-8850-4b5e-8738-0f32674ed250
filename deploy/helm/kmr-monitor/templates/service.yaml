{{- range .Values.StateMetrics.deployments }}
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/component: exporter
    app.kubernetes.io/name: {{ .name }}
    app.kubernetes.io/version: {{ $.Values.StateMetrics.version }}
  name: {{ .name }}
  namespace: {{ $.Release.Namespace }}
spec:
  #  clusterIP: None
  ports:
    - name: http-metrics
      port: {{ $.Values.StateMetrics.httpPort }}
      targetPort: http-metrics
    - name: telemetry
      port: {{ $.Values.StateMetrics.telemetryPort }}
      targetPort: telemetry
  selector:
    app.kubernetes.io/name: {{ .name }}
{{- end }}

---
apiVersion: v1
kind: Service
metadata:
  name: vmagent
  namespace: {{ .Release.Namespace }}
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: {{ .Values.Vmagent.httpPort | quote}}
spec:
  selector:
    app: vmagent
  clusterIP: None
  ports:
    - name: http
      port: {{ .Values.Vmagent.httpPort }}
      targetPort: http