apiVersion: apps/v1
kind: DaemonSet
metadata:
  labels:
    app.kubernetes.io/component: exporter
    app.kubernetes.io/name: kmr-kube-state-metrics
    app.kubernetes.io/version: {{ .Values.StateMetrics.version }}
  name: kmr-kube-state-metrics-shard
  namespace: kmr-monitor
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: kmr-kube-state-metrics
  template:
    metadata:
      labels:
        app.kubernetes.io/component: exporter
        app.kubernetes.io/name: kmr-kube-state-metrics
        app.kubernetes.io/version: {{ .Values.StateMetrics.version }}
    spec:
      automountServiceAccountToken: true
      containers:
        - args:
            - --resources=pods
            - --node=$(NODE_NAME)
            - --metric-labels-allowlist=pods=[*],namespaces=[*],nodes=[*],services=[*],jobs=[*]
            - --metric-annotations-allowlist=pods=[*],namespaces=[*],nodes=[*],services=[*],jobs=[*]
          env:
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.nodeName
          image: "{{ .Values.StateMetrics.imageRepositoryHost}}/{{ .Values.StateMetrics.repository }}:{{ .Values.StateMetrics.tag }}"
          livenessProbe:
            httpGet:
              path: /livez
              port: http-metrics
            initialDelaySeconds: 5
            timeoutSeconds: 5
          name: kmr-kube-state-metrics-shard
          ports:
            - containerPort: {{ .Values.StateMetrics.httpPort }}
              name: http-metrics
            - containerPort: {{ .Values.StateMetrics.telemetryPort }}
              name: telemetry
          readinessProbe:
            httpGet:
              path: /readyz
              port: telemetry
            initialDelaySeconds: 5
            timeoutSeconds: 5
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - ALL
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            runAsUser: 65534
            seccompProfile:
              type: RuntimeDefault
          resources:
            limits:
              cpu: 100m
              memory: 1024Mi
            requests:
              cpu: 10m
              memory: 50Mi
      nodeSelector:
        {{ toYaml .Values.StateMetrics.pod.nodeSelector | nindent 8}}
      tolerations:
        {{ toYaml .Values.StateMetrics.pod.tolerations | nindent 8 }}
      serviceAccountName: kmr-kube-state-metrics