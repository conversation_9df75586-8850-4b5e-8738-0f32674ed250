apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    app.kubernetes.io/component: exporter
    app.kubernetes.io/name: kmr-kube-state-metrics
    app.kubernetes.io/version:  {{ .Values.StateMetrics.version }}
  name: kmr-kube-state-metrics
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kmr-kube-state-metrics
subjects:
  - kind: ServiceAccount
    name: kmr-kube-state-metrics
    namespace: {{ .Release.Namespace }}

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: vmagent-clusterrolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kmr-kube-state-metrics
subjects:
  - kind: ServiceAccount
    name: kmr-monitor
    namespace: {{ .Release.Namespace }}