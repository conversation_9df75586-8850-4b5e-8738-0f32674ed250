apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Release.Name }}-config

data:
  scrape.yml: |
    global:
      scrape_interval: 30s
      scrape_timeout: 30s
      external_labels:
        kenv:  {{ .Values.Vmagent.env }}

    scrape_configs:
      - job_name: kube-nodes
        kubernetes_sd_configs:
          - role: node
        relabel_configs:
          - source_labels: [__address__]
            regex: "(.*):10250"
            replacement: "${1}:9101"
            target_label: __address__
            action: replace
          - action: labelmap
            regex: __meta_kubernetes_node_label_(.+)
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: namespace
          - source_labels: [__meta_kubernetes_node_name]
            action: replace
            target_label: node
      - job_name: 'kube-state-metrics'
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_label_app_kubernetes_io_name]
            action: keep
            regex: kmr-kube-state-metrics.*
          - source_labels: [__meta_kubernetes_pod_label_app_kubernetes_io_name]
            action: replace
            target_label: service
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: namespace
          - source_labels: [__meta_kubernetes_pod_host_ip]
            target_label: instance
            action: replace
      - job_name: 'kube-cadvisor'
        kubernetes_sd_configs:
          - role: node
        relabel_configs:
          - source_labels: [__address__]
            regex: '(.*):10250'
            replacement: '${1}:10255'
            target_label: __address__
            action: replace
          - action: labelmap
            regex: __meta_kubernetes_node_label_(.+)
            replacement: $1
          - replacement: /metrics/cadvisor
            target_label: __metrics_path__
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: namespace
          - source_labels: [__meta_kubernetes_node_name]
            action: replace
            target_label: node
      - job_name: 'kubelet'
        kubernetes_sd_configs:
          - role: node
        relabel_configs:
          - source_labels: [__address__]
            regex: '(.*):10250'
            replacement: '${1}:10255'
            target_label: __address__
            action: replace
          - action: labelmap
            regex: __meta_kubernetes_node_label_(.+)
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: namespace
          - source_labels: [__meta_kubernetes_node_name]
            action: replace
            target_label: node
      - job_name: 'kube-scheduler'
        kubernetes_sd_configs:
          - role: pod
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
          insecure_skip_verify: true
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_label_component, __meta_kubernetes_pod_labelpresent_component]
            regex: (kube-scheduler);true
            action: keep
          - source_labels: [__address__]
            regex: '(.+)'
            replacement: '${1}:10259'
            target_label: __address__
            action: replace
          - source_labels: [__meta_kubernetes_pod_host_ip]
            target_label: instance
            action: replace
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: namespace
          - action: labelmap
            regex: __meta_kubernetes_node_label_(.+)
          - source_labels: [__meta_kubernetes_pod_node_name]
            action: replace
            target_label: node
      - job_name: 'kube-controller-manager'
        kubernetes_sd_configs:
          - role: pod
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
          insecure_skip_verify: true
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_label_component, __meta_kubernetes_pod_labelpresent_component]
            regex: (kube-controller-manager);true
            action: keep
          - source_labels: [__address__]
            regex: '(.+)'
            replacement: '${1}:10257'
            target_label: __address__
            action: replace
          - source_labels: [__meta_kubernetes_pod_host_ip]
            target_label: instance
            action: replace
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: namespace
          - action: labelmap
            regex: __meta_kubernetes_node_label_(.+)
          - source_labels: [__meta_kubernetes_pod_node_name]
            action: replace
            target_label: node
      - job_name: 'kube-etcd'
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_label_component]
            action: keep
            regex: etcd
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: namespace
          - source_labels: [__address__]
            regex: '(.*)'
            replacement: '${1}:2379'
            target_label: __address__
            action: replace
          - source_labels: [__meta_kubernetes_pod_host_ip]
            target_label: instance
            action: replace
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - source_labels: [__meta_kubernetes_pod_node_name]
            action: replace
            target_label: node
      - job_name: 'kube-coredns'
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_label_k8s_app]
            action: keep
            regex: coredns
          - source_labels: [__meta_kubernetes_pod_container_port_protocol]
            action: keep
            regex: TCP
          - source_labels: [__meta_kubernetes_pod_container_port_name]
            action: keep
            regex: metrics
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: namespace
          - source_labels: [__meta_kubernetes_pod_host_ip]
            target_label: instance
            action: replace
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - source_labels: [__meta_kubernetes_pod_node_name]
            action: replace
            target_label: node
      - job_name: 'volcano-scheduler'
        kubernetes_sd_configs:
          - role: endpoints
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: "volcano-scheduler-service"
          - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
            action: replace
            target_label: __scheme__
            regex: (https?)
          - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_path]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [__address__, __meta_kubernetes_service_annotation_prometheus_io_port]
            action: replace
            target_label: __address__
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
          - action: labelmap
            regex: __meta_kubernetes_service_label_(.+)
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: kubernetes_namespace
          - source_labels: [__meta_kubernetes_service_name]
            action: replace
            target_label: kubernetes_name
          - source_labels: [__meta_kubernetes_node_name]
            action: replace
            target_label: node
      - job_name: 'kube-apiserver'
        kubernetes_sd_configs:
          - role: pod
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
          insecure_skip_verify: true
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
          - source_labels: [ __meta_kubernetes_pod_label_component, __meta_kubernetes_pod_labelpresent_component ]
            action: keep
            regex: kube-apiserver;true
          - source_labels: [ __address__ ]
            target_label: __address__
            replacement: ${1}:6443
            regex: (.+)
          - source_labels: [ __meta_kubernetes_pod_host_ip ]
            target_label: instance
            action: replace
          - source_labels: [ __meta_kubernetes_namespace ]
            action: replace
            target_label: namespace
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - source_labels: [__meta_kubernetes_pod_node_name]
            action: replace
            target_label: node
      - job_name: "spark-app"
        honor_labels: false
        kubernetes_sd_configs:
          - role: pod
        scheme: http
        relabel_configs:
          - source_labels: ["__meta_kubernetes_pod_annotation_prometheus_io_scrape"]
            action: keep
            regex: true
          - source_labels: ["__meta_kubernetes_pod_label_spark_app_name"]
            action: keep
            regex: (.+)
          - source_labels: ["__meta_kubernetes_pod_annotation_prometheus_io_path"]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [ "__address__", "__meta_kubernetes_pod_annotation_prometheus_io_port" ]
            action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            target_label: __address__
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - source_labels: ["__meta_kubernetes_namespace"]
            action: replace
            target_label: namespace
          - source_labels: ["__meta_kubernetes_pod_name"]
            action: replace
            target_label: pod
          - source_labels: [__meta_kubernetes_pod_node_name]
            action: replace
            target_label: node
      - job_name: "spark-operator"
        honor_labels: false
        kubernetes_sd_configs:
          - role: pod
        scheme: http
        relabel_configs:
          - source_labels: ["__meta_kubernetes_pod_annotation_prometheus_io_scrape"]
            action: keep
            regex: true
          - source_labels: ["__meta_kubernetes_namespace"]
            action: keep
            regex: spark-operator
          - source_labels: ["__meta_kubernetes_pod_annotation_prometheus_io_path"]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [ "__address__", "__meta_kubernetes_pod_annotation_prometheus_io_port" ]
            action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            target_label: __address__
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - source_labels: ["__meta_kubernetes_namespace"]
            action: replace
            target_label: namespace
          - source_labels: ["__meta_kubernetes_pod_name"]
            action: replace
            target_label: pod
          - source_labels: [__meta_kubernetes_pod_node_name]
            action: replace
            target_label: node
      - job_name: 'vmagent'
        honor_labels: false
        kubernetes_sd_configs:
          - role: pod
        scheme: http
        relabel_configs:
          - source_labels: ["__meta_kubernetes_pod_annotation_prometheus_io_scrape"]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_pod_label_app]
            action: keep
            regex: vmagent
          - action: labelmap
            regex: __meta_kubernetes_service_label_(.+)
          - source_labels: ["__meta_kubernetes_namespace"]
            action: replace
            target_label: namespace
          - source_labels: ["__meta_kubernetes_pod_name"]
            action: replace
            target_label: pod
          - source_labels: [__meta_kubernetes_pod_node_name]
            action: replace
            target_label: node
          - source_labels: [__meta_kubernetes_pod_ip]
            target_label: instance
            action: replace
      - job_name: 'kube-ray'
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_label_ray_io_is_ray_node]
            action: keep
            regex: 'yes'
          - source_labels: [__meta_kubernetes_pod_label_ray_io_node_type]
            action: keep
            regex: head|worker
          - source_labels: [ __meta_kubernetes_pod_container_port_name]
            action: keep
            regex: metrics
          - source_labels: [__meta_kubernetes_pod_label_ray_io_node_type]
            action: replace
            target_label: ray_node_type
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: namespace
          - source_labels: [__meta_kubernetes_pod_host_ip]
            target_label: ip
            action: replace
          - source_labels: [__meta_kubernetes_pod_node_name]
            action: replace
            target_label: node
          - source_labels: [__meta_kubernetes_pod_name]
            action: replace
            target_label: pod
          - source_labels: [__meta_kubernetes_pod_label_ray_io_cluster]
            action: replace
            target_label: ray_cluster
      - job_name: 'kube-ray-autoscaler'
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_label_ray_io_is_ray_node]
            action: keep
            regex: 'yes'
          - source_labels: [__meta_kubernetes_pod_label_ray_io_node_type]
            action: keep
            regex: head
          - source_labels: [ __meta_kubernetes_pod_container_port_name]
            action: keep
            regex: metrics
          - source_labels: [ __address__ ]
            regex: '(.*):8080'
            replacement: '${1}:44217'
            target_label: __address__
          - source_labels: [__meta_kubernetes_pod_label_ray_io_node_type]
            action: replace
            target_label: ray_node_type
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: namespace
          - source_labels: [__meta_kubernetes_pod_host_ip]
            target_label: ip
            action: replace
          - source_labels: [__meta_kubernetes_pod_node_name]
            action: replace
            target_label: node
          - source_labels: [__meta_kubernetes_pod_name]
            action: replace
            target_label: pod
          - source_labels: [__meta_kubernetes_pod_label_ray_io_cluster]
            action: replace
            target_label: ray_cluster
      - job_name: 'kube-ray-dashboard'
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_label_ray_io_is_ray_node]
            action: keep
            regex: 'yes'
          - source_labels: [__meta_kubernetes_pod_label_ray_io_node_type]
            action: keep
            regex: head
          - source_labels: [ __meta_kubernetes_pod_container_port_name]
            action: keep
            regex: metrics
          - source_labels: [ __address__ ]
            regex: '(.*):8080'
            replacement: '${1}:44227'
            target_label: __address__
          - source_labels: [__meta_kubernetes_pod_label_ray_io_node_type]
            action: replace
            target_label: ray_node_type
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: namespace
          - source_labels: [__meta_kubernetes_pod_host_ip]
            target_label: ip
            action: replace
          - source_labels: [__meta_kubernetes_pod_node_name]
            action: replace
            target_label: node
          - source_labels: [__meta_kubernetes_pod_name]
            action: replace
            target_label: pod
          - source_labels: [__meta_kubernetes_pod_label_ray_io_cluster]
            action: replace
            target_label: ray_cluster
      - job_name: 'kube-prometheus-prometheus'
        kubernetes_sd_configs:
          - role: endpoints
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_label_app, __meta_kubernetes_service_labelpresent_app]
            separator: ;
            regex: (kube-prometheus-stack-prometheus);true
            replacement: $1
            action: keep
          - source_labels: [__meta_kubernetes_service_label_release, __meta_kubernetes_service_labelpresent_release]
            separator: ;
            regex: (prometheus);true
            replacement: $1
            action: keep
          - source_labels: [__meta_kubernetes_service_label_self_monitor, __meta_kubernetes_service_labelpresent_self_monitor]
            separator: ;
            regex: (true);true
            replacement: $1
            action: keep
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            separator: ;
            regex: http-web
            replacement: $1
            action: keep
          - source_labels: [__meta_kubernetes_endpoint_address_target_kind, __meta_kubernetes_endpoint_address_target_name]
            separator: ;
            regex: Node;(.*)
            target_label: node
            replacement: ${1}
            action: replace
          - source_labels: [__meta_kubernetes_endpoint_address_target_kind, __meta_kubernetes_endpoint_address_target_name]
            separator: ;
            regex: Pod;(.*)
            target_label: pod
            replacement: ${1}
            action: replace
          - source_labels: [__meta_kubernetes_namespace]
            separator: ;
            target_label: namespace
            replacement: $1
            action: replace
          - source_labels: [__meta_kubernetes_service_name]
            separator: ;
            target_label: service
            replacement: $1
            action: replace
          - source_labels: [__meta_kubernetes_pod_name]
            separator: ;
            target_label: pod
            replacement: $1
            action: replace
          - source_labels: [__meta_kubernetes_pod_container_name]
            separator: ;
            target_label: container
            replacement: $1
            action: replace
          - source_labels: [__meta_kubernetes_pod_phase]
            separator: ;
            regex: (Failed|Succeeded)
            replacement: $1
            action: drop
          - source_labels: [__meta_kubernetes_service_name]
            separator: ;
            target_label: job
            replacement: ${1}
            action: replace
          - separator: ;
            target_label: endpoint
            replacement: http-web
            action: replace
          - source_labels: [__address__]
            separator: ;
            modulus: 1
            target_label: __tmp_hash
            replacement: $1
            action: hashmod
          - source_labels: [__tmp_hash]
            separator: ;
            regex: "0"
            replacement: $1
            action: keep
      - job_name: 'kube-prometheus-prometheus-grafana'
        kubernetes_sd_configs:
          - role: endpoints
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_label_app_kubernetes_io_name, __meta_kubernetes_service_labelpresent_app_kubernetes_io_name]
            separator: ;
            regex: (grafana);true
            replacement: $1
            action: keep
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            separator: ;
            regex: http-web
            replacement: $1
            action: keep
          - source_labels: [__meta_kubernetes_endpoint_address_target_kind, __meta_kubernetes_endpoint_address_target_name]
            separator: ;
            regex: Node;(.*)
            target_label: node
            replacement: ${1}
            action: replace
          - source_labels: [__meta_kubernetes_endpoint_address_target_kind, __meta_kubernetes_endpoint_address_target_name]
            separator: ;
            regex: Pod;(.*)
            target_label: pod
            replacement: ${1}
            action: replace
          - source_labels: [__meta_kubernetes_namespace]
            separator: ;
            target_label: namespace
            replacement: $1
            action: replace
          - source_labels: [__meta_kubernetes_service_name]
            separator: ;
            target_label: service
            replacement: $1
            action: replace
          - source_labels: [__meta_kubernetes_pod_name]
            separator: ;
            target_label: pod
            replacement: $1
            action: replace
          - source_labels: [__meta_kubernetes_pod_container_name]
            separator: ;
            target_label: container
            replacement: $1
            action: replace
          - source_labels: [__meta_kubernetes_pod_phase]
            separator: ;
            regex: (Failed|Succeeded)
            replacement: $1
            action: drop
          - source_labels: [__meta_kubernetes_service_name]
            separator: ;
            target_label: job
            replacement: ${1}
            action: replace
          - separator: ;
            target_label: endpoint
            replacement: http-web
            action: replace
          - source_labels: [__address__]
            separator: ;
            modulus: 1
            target_label: __tmp_hash
            replacement: $1
            action: hashmod
          - source_labels: [__tmp_hash]
            separator: ;
            regex: "0"
            replacement: $1
            action: keep
      - job_name: 'kube-node-local-dns'
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_label_k8s_app]
            action: keep
            regex: node-local-dns
          - source_labels: ["__meta_kubernetes_pod_annotation_prometheus_io_scrape"]
            action: keep
            regex: true
          - source_labels: [ "__address__", "__meta_kubernetes_pod_annotation_prometheus_io_port" ]
            action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            target_label: __address__
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: namespace
          - source_labels: [__meta_kubernetes_pod_host_ip]
            target_label: instance
            action: replace
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - source_labels: [__meta_kubernetes_pod_node_name]
            action: replace
            target_label: node
      - job_name: "juicefs-sync"
        honor_labels: false
        kubernetes_sd_configs:
          - role: pod
        scheme: http
        relabel_configs:
          - source_labels: ["__meta_kubernetes_pod_annotation_prometheus_io_scrape"]
            action: keep
            regex: true
          - source_labels: ["__meta_kubernetes_pod_label_role"]
            action: keep
            regex: juicefs-sync-job-manager
          - source_labels: ["__meta_kubernetes_pod_annotation_prometheus_io_path"]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [ "__address__", "__meta_kubernetes_pod_annotation_prometheus_io_port" ]
            action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            target_label: __address__
          - source_labels: ["__meta_kubernetes_pod_annotation_prometheus_io_account_id"]
            target_label: vm_account_id
            action: replace
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - source_labels: ["__meta_kubernetes_namespace"]
            action: replace
            target_label: namespace
          - source_labels: ["__meta_kubernetes_pod_name"]
            action: replace
            target_label: pod
          - source_labels: [__meta_kubernetes_pod_node_name]
            action: replace
            target_label: node
      - job_name: 'celeborn-master'
        kubernetes_sd_configs:
          - role: pod
            namespaces:
              names:
                - celeborn-spark
        relabel_configs:
          - source_labels: [ __meta_kubernetes_pod_label_app_kubernetes_io_instance ]
            action: keep
            regex: celeborn
          - source_labels: [ __meta_kubernetes_pod_label_app_kubernetes_io_name ]
            action: keep
            regex: celeborn
          - source_labels: [ __meta_kubernetes_pod_label_app_kubernetes_io_role ]
            action: keep
            regex: master
          - source_labels: [ __meta_kubernetes_pod_ip ]
            action: replace
            target_label: __address__
            replacement: '$1:9098'
          - source_labels: [ __meta_kubernetes_pod_annotation_prometheus_io_path ]
            action: replace
            target_label: __metrics_path__
            regex: (.*)
            replacement: /metrics/prometheus
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
      - job_name: 'celeborn-worker'
        kubernetes_sd_configs:
          - role: pod
            namespaces:
              names:
                - celeborn-spark
        relabel_configs:
          - source_labels: [ __meta_kubernetes_pod_label_app_kubernetes_io_instance ]
            action: keep
            regex: celeborn
          - source_labels: [ __meta_kubernetes_pod_label_app_kubernetes_io_name ]
            action: keep
            regex: celeborn
          - source_labels: [ __meta_kubernetes_pod_label_app_kubernetes_io_role ]
            action: keep
            regex: worker
          - source_labels: [ __meta_kubernetes_pod_ip ]
            action: replace
            target_label: __address__
            replacement: '$1:9096'
          - source_labels: [ __meta_kubernetes_pod_annotation_prometheus_io_path ]
            action: replace
            target_label: __metrics_path__
            regex: (.*)
            replacement: /metrics/prometheus
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
      - job_name: "flink-operator"
        honor_labels: false
        kubernetes_sd_configs:
          - role: pod
        scheme: http
        relabel_configs:
          - source_labels: ["__meta_kubernetes_pod_annotation_prometheus_io_scrape"]
            action: keep
            regex: true
          - source_labels: ["__meta_kubernetes_pod_label_app_kubernetes_io_name"]
            action: keep
            regex: flink-kubernetes-operator
          - source_labels: ["__meta_kubernetes_pod_annotation_prometheus_io_path"]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [ "__address__", "__meta_kubernetes_pod_annotation_prometheus_io_port" ]
            action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            target_label: __address__
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - source_labels: ["__meta_kubernetes_namespace"]
            action: replace
            target_label: namespace
          - source_labels: ["__meta_kubernetes_pod_name"]
            action: replace
            target_label: pod
          - source_labels: [__meta_kubernetes_pod_node_name]
            action: replace
            target_label: node
      - job_name: "flink-app"
        honor_labels: false
        kubernetes_sd_configs:
          - role: pod
        scheme: http
        relabel_configs:
          - source_labels: ["__meta_kubernetes_pod_annotation_prometheus_io_scrape"]
            action: keep
            regex: true
          - source_labels: ["__meta_kubernetes_pod_label_component"]
            action: keep
            regex: jobmanager|taskmanager
          - source_labels: ["__meta_kubernetes_pod_annotation_prometheus_io_path"]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [ "__address__", "__meta_kubernetes_pod_annotation_prometheus_io_port" ]
            action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            target_label: __address__
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - source_labels: ["__meta_kubernetes_namespace"]
            action: replace
            target_label: namespace
          - source_labels: ["__meta_kubernetes_pod_name"]
            action: replace
            target_label: pod
          - source_labels: [__meta_kubernetes_pod_node_name]
            action: replace
            target_label: node
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Release.Name }}-sidecar-config

data:
  conf.yaml: |-
    service:
      Version: {{ .Values.SidecarImage.tag  | quote  }}
      HttpPort: {{ .Values.Sidecar.httpPort  }}
      RunMode: {{ .Values.Sidecar.RunMode  | quote  }}
      PProf:  {{ .Values.Sidecar.PProf }}
      Env: {{ .Values.Sidecar.Env | quote }}
      Prefix: {{ .Values.Sidecar.Prefix  | quote  }}
      RemoteWrite:  {{ .Values.Sidecar.RemoteWrite | toJson }}

    log:
      path: {{ .Values.Sidecar.path  | quote  }}
      maxSize:  {{ .Values.Sidecar.maxSize  }}
      maxBackups: {{ .Values.Sidecar.maxBackups  }}
      maxAge: {{ .Values.Sidecar.maxAge  }}
      console: {{ .Values.Sidecar.console  }}
      file: {{ .Values.Sidecar.file  }}
    RemoteWrites:
      vminsert:
        {{- range $key, $value := .Values.Sidecar.RemoteWrites.vminsert }}
        {{ $key }}: {{$value | quote}}
        {{- end }}
      kafka:
        {{- range $key, $value := .Values.Sidecar.RemoteWrites.kafka }}
        {{ $key }}: {{$value | quote}}
        {{- end }}
      datatransfer:
        {{- range $key, $value := .Values.Sidecar.RemoteWrites.datatransfer }}
        {{ $key }}: {{$value | quote}}
        {{- end }}



