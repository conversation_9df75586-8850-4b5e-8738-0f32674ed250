apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: vmagent-ingress
  namespace: kmr-monitor
  labels:
    app: vmagent
    component: ingress
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: "0"  # 禁用请求体大小限制
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
spec:
  ingressClassName: serverless-ingress
  rules:
    - host: vmagent-0.grafana.serverless-1-pre.ksyun.com
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: vmagent-0
                port:
                  number: 8429
    - host: vmagent-1.grafana.serverless-1-pre.ksyun.com
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: vmagent-1
                port:
                  number: 8429
    - host: vmagent-2.grafana.serverless-1-pre.ksyun.com
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: vmagent-2
                port:
                  number: 8429