# Default values for tuna-apiserver.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1


VMImage:
  imageRepositoryHost: 	hub-vpc-cn-beijing-6.kce.ksyun.com
  repository: cbd-serverless-tools/vmagent
  tag: 1.113.0

SidecarImage:
  imageRepositoryHost: 	hub-vpc-cn-beijing-6.kce.ksyun.com
  repository: cbd-serverless-spark/metrics-sidecar
  tag: 0.2.1

Vmagent:
  name: kmr-monitor
  remoteAddr: http://victoriametrics-serverless-internal.ksyun.com:8479/insert/multitenant/prometheus/api/v1/write
  env: serverless-online-1-1
  replicas: 3
  httpPort: 8429
  pod:
    nodeSelector:
      kubernetes.io/os: linux
      dedicated: serverless-operator
    tolerations:
      - key: "dedicated"
        operator: "Equal"
        value: "serverless-operator"
        effect: "NoSchedule"


StateMetrics:
  imageRepositoryHost: 	hub-vpc-cn-beijing-6.kce.ksyun.com
  repository: cbd-serverless-tools/kube-state-metrics
  tag: v2.10.1
  httpPort: 8080
  telemetryPort: 8081
  version: 2.10.1
  pod:
    nodeSelector:
      kubernetes.io/os: linux
    tolerations:
      - operator: Exists

Sidecar:
  name: kmr-monitor
  httpPort: 8428
  RunMode: "release"
  PProf: true
  path: "/data/project/metrics-sidecar"
  maxSize: 200
  maxBackups: 5
  maxAge: 1
  console: true
  file: true
  Env: "serverless-online-1-1"
  Prefix: "spark_"
  RemoteWrite:
    - vminsert
  RemoteWrites:
    vminsert:
      RemoteWrite: "http://************:10000/insert/0/prometheus/api/v1/write"
      Compressor: "snappy"
    kafka:
      Topic: "test-monitor"
      Brokers: "*************:9092,*************:9092"
      GroupID: "test"
      Compressor: "snappy"
    datatransfer:
      RemoteWrite: "http://data-transfer.inner.sdns.ksyun.com/inner/log/put"
      Auth: "bQeuZXJfb2FzaXM6a1I4VE1jc2E5NHRmQ1d4iu=="
      CluterId: "3291e6f6-d997-4216-8286-8550b9e5dd8d"
