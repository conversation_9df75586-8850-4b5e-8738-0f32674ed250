// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.kingsoft.com/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/v1/clusters": {
            "get": {
                "description": "获取集群列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "集群"
                ],
                "summary": "获取集群列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "分页偏移量",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "所有者ID",
                        "name": "account_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/model.Cluster"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "description": "创建新集群并可选择部署指定的服务，支持配置AK/SK/logDirectory信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "集群"
                ],
                "summary": "创建集群",
                "parameters": [
                    {
                        "description": "集群信息，包含AK/SK/logDirectory等配置",
                        "name": "cluster",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.CreateClusterRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/v1.CreateClusterResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/clusters/{id}": {
            "get": {
                "description": "通过ID获取集群信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "集群"
                ],
                "summary": "通过ID获取集群",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/model.Cluster"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "description": "更新集群信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "集群"
                ],
                "summary": "更新集群",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "集群信息",
                        "name": "cluster",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.UpdateClusterRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/model.Cluster"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "description": "删除指定的集群，并自动卸载所有已安装的Operator",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "集群"
                ],
                "summary": "删除集群",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/clusters/{id}/info": {
            "put": {
                "description": "更新集群的AK/SK/logDirectory等配置信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "集群"
                ],
                "summary": "更新集群信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "集群信息",
                        "name": "clusterInfo",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.UpdateClusterInfoRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/model.Cluster"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/clusters/{id}/operators": {
            "post": {
                "description": "在指定的集群上安装指定类型的Operator",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "集群"
                ],
                "summary": "安装集群Operator",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "安装Operator请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.InstallClusterOperatorRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/clusters/{id}/operators/{operator_type}": {
            "delete": {
                "description": "从指定的集群上卸载指定类型的Operator",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "集群"
                ],
                "summary": "卸载集群Operator",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Operator类型",
                        "name": "operator_type",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/clusters/{id}/operators/{operator_type}/status": {
            "get": {
                "description": "获取指定集群上指定类型Operator的安装状态",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "集群"
                ],
                "summary": "获取集群Operator状态",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Operator类型",
                        "name": "operator_type",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/clusters/{id}/validate": {
            "post": {
                "description": "验证指定集群的连接是否正常",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "集群"
                ],
                "summary": "验证集群连接",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/health": {
            "get": {
                "description": "检查 API 服务是否正常运行",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "系统"
                ],
                "summary": "健康检查",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.HealthResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/jobs": {
            "get": {
                "description": "获取作业列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "作业"
                ],
                "summary": "获取作业列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "分页偏移量",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "集群ID",
                        "name": "cluster_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "所有者ID",
                        "name": "account_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "作业类型",
                        "name": "job_type",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "作业状态",
                        "name": "status",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/model.Job"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "description": "创建新作业",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "作业"
                ],
                "summary": "创建作业",
                "parameters": [
                    {
                        "description": "作业信息",
                        "name": "job",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.CreateJobRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/model.Job"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/jobs/{id}": {
            "get": {
                "description": "通过ID获取作业信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "作业"
                ],
                "summary": "通过ID获取作业",
                "parameters": [
                    {
                        "type": "string",
                        "description": "作业ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/model.Job"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "description": "更新作业状态",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "作业"
                ],
                "summary": "更新作业",
                "parameters": [
                    {
                        "type": "string",
                        "description": "作业ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "作业状态",
                        "name": "job",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.UpdateJobRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/model.Job"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "description": "删除指定的作业",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "作业"
                ],
                "summary": "删除作业",
                "parameters": [
                    {
                        "type": "string",
                        "description": "作业ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/jobs/{id}/cancel": {
            "post": {
                "description": "取消正在运行的作业",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "作业"
                ],
                "summary": "取消作业",
                "parameters": [
                    {
                        "type": "string",
                        "description": "作业ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/jobs/{id}/submit": {
            "post": {
                "description": "提交作业到集群",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "作业"
                ],
                "summary": "提交作业",
                "parameters": [
                    {
                        "type": "string",
                        "description": "作业ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "所有者ID",
                        "name": "account_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/workflows": {
            "get": {
                "description": "获取工作流列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "工作流"
                ],
                "summary": "获取工作流列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "分页偏移量",
                        "name": "offset",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "集群ID",
                        "name": "cluster_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "所有者ID",
                        "name": "account_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "工作流状态",
                        "name": "status",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/model.Workflow"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "description": "创建新工作流",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "工作流"
                ],
                "summary": "创建工作流",
                "parameters": [
                    {
                        "description": "工作流信息",
                        "name": "workflow",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.CreateWorkflowRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/model.Workflow"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/workflows/argo-webui-url": {
            "get": {
                "description": "获取指定集群的 Argo Workflows Web UI 访问地址",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "工作流"
                ],
                "summary": "获取 Argo Workflows Web UI URL",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群ID",
                        "name": "cluster_id",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.ArgoWebUIURLResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/workflows/clusters": {
            "post": {
                "description": "异步创建新集群并可选择部署指定的服务，使用Argo Workflows执行，返回工作流ID用于查询进度",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "工作流"
                ],
                "summary": "异步创建集群",
                "parameters": [
                    {
                        "description": "集群信息",
                        "name": "cluster",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.CreateClusterAsyncRequest"
                        }
                    }
                ],
                "responses": {
                    "202": {
                        "description": "Accepted",
                        "schema": {
                            "$ref": "#/definitions/v1.AsyncWorkflowResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/workflows/clusters/delete": {
            "post": {
                "description": "异步删除指定的集群，并自动卸载所有已安装的Operator，使用Argo Workflows执行，返回工作流ID用于查询进度",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "工作流"
                ],
                "summary": "异步删除集群",
                "parameters": [
                    {
                        "description": "删除请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.DeleteClusterAsyncRequest"
                        }
                    }
                ],
                "responses": {
                    "202": {
                        "description": "Accepted",
                        "schema": {
                            "$ref": "#/definitions/v1.AsyncWorkflowResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/workflows/operators/install": {
            "post": {
                "description": "在指定的集群上异步安装指定类型的Operator，使用Argo Workflows执行，返回工作流ID用于查询进度",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "工作流"
                ],
                "summary": "异步安装Operator",
                "parameters": [
                    {
                        "description": "安装请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.OperatorAsyncRequest"
                        }
                    }
                ],
                "responses": {
                    "202": {
                        "description": "Accepted",
                        "schema": {
                            "$ref": "#/definitions/v1.AsyncWorkflowResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/workflows/operators/uninstall": {
            "post": {
                "description": "从指定的集群上异步卸载指定类型的Operator，使用Argo Workflows执行，返回工作流ID用于查询进度",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "工作流"
                ],
                "summary": "异步卸载Operator",
                "parameters": [
                    {
                        "description": "卸载请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.OperatorAsyncRequest"
                        }
                    }
                ],
                "responses": {
                    "202": {
                        "description": "Accepted",
                        "schema": {
                            "$ref": "#/definitions/v1.AsyncWorkflowResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/workflows/{id}": {
            "get": {
                "description": "通过ID获取工作流信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "工作流"
                ],
                "summary": "通过ID获取工作流",
                "parameters": [
                    {
                        "type": "string",
                        "description": "工作流ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/model.Workflow"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "description": "更新工作流信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "工作流"
                ],
                "summary": "更新工作流",
                "parameters": [
                    {
                        "type": "string",
                        "description": "工作流ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "工作流信息",
                        "name": "workflow",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.UpdateWorkflowRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/model.Workflow"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "description": "删除指定的工作流",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "工作流"
                ],
                "summary": "删除工作流",
                "parameters": [
                    {
                        "type": "string",
                        "description": "工作流ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/workflows/{id}/cancel": {
            "post": {
                "description": "取消正在运行的工作流",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "工作流"
                ],
                "summary": "取消工作流",
                "parameters": [
                    {
                        "type": "string",
                        "description": "工作流ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/workflows/{id}/retry": {
            "post": {
                "description": "重试失败的工作流或特定步骤，支持重试整个工作流或单个步骤",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "工作流"
                ],
                "summary": "重试工作流",
                "parameters": [
                    {
                        "type": "string",
                        "description": "工作流ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "重试请求",
                        "name": "request",
                        "in": "body",
                        "schema": {
                            "$ref": "#/definitions/v1.RetryWorkflowRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/workflows/{id}/status": {
            "put": {
                "description": "更新工作流状态",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "工作流"
                ],
                "summary": "更新工作流状态",
                "parameters": [
                    {
                        "type": "string",
                        "description": "工作流ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "工作流状态",
                        "name": "workflow",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/v1.UpdateWorkflowStatusRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/model.Workflow"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/workflows/{id}/submit": {
            "post": {
                "description": "提交工作流到集群",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "工作流"
                ],
                "summary": "提交工作流",
                "parameters": [
                    {
                        "type": "string",
                        "description": "工作流ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/workflows/{id}/sync": {
            "post": {
                "description": "从Argo Workflows同步工作流状态到数据库",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "工作流"
                ],
                "summary": "同步工作流状态",
                "parameters": [
                    {
                        "type": "string",
                        "description": "工作流ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/v1.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/v1.ErrorResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "model.Cluster": {
            "type": "object",
            "properties": {
                "account_id": {
                    "description": "用户ID，记录谁创建了此集群",
                    "type": "string"
                },
                "cluster_info": {
                    "description": "集群信息，存储AK/SK/logDirectory等",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "created_at": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "kce_cluster_id": {
                    "description": "外部系统的集群ID",
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "services": {
                    "description": "已安装的服务列表，如[\"spark\", \"flink\"]",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "status": {
                    "description": "pending, active, error",
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "model.Job": {
            "type": "object",
            "properties": {
                "account_id": {
                    "type": "string"
                },
                "args": {
                    "type": "string"
                },
                "cluster_id": {
                    "type": "string"
                },
                "command": {
                    "type": "string"
                },
                "config": {
                    "description": "JSON 格式的配置",
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "end_time": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "logs": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "name": {
                    "type": "string"
                },
                "namespace": {
                    "type": "string"
                },
                "runtime": {
                    "description": "运行时长（秒）",
                    "type": "integer"
                },
                "start_time": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "type": {
                    "description": "spark, flink",
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "model.Workflow": {
            "type": "object",
            "properties": {
                "account_id": {
                    "type": "string"
                },
                "cluster": {
                    "$ref": "#/definitions/model.Cluster"
                },
                "cluster_id": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "definition": {
                    "description": "JSON 格式的工作流定义",
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "end_time": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "namespace": {
                    "type": "string"
                },
                "resource_id": {
                    "description": "工作流资源ID",
                    "type": "string"
                },
                "start_time": {
                    "type": "string"
                },
                "status": {
                    "description": "pending, running, completed, failed",
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "v1.ArgoWebUIURLResponse": {
            "type": "object",
            "properties": {
                "cluster_id": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "web_ui_url": {
                    "type": "string"
                }
            }
        },
        "v1.AsyncWorkflowResponse": {
            "type": "object",
            "properties": {
                "argo_workflow": {
                    "type": "string"
                },
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "workflow_id": {
                    "type": "string"
                },
                "workflow_name": {
                    "type": "string"
                }
            }
        },
        "v1.CreateClusterAsyncRequest": {
            "type": "object",
            "required": [
                "account_id",
                "kce_cluster_id",
                "name"
            ],
            "properties": {
                "account_id": {
                    "type": "string"
                },
                "ak": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "kce_cluster_id": {
                    "type": "string"
                },
                "log_directory": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "services": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "sk": {
                    "type": "string"
                }
            }
        },
        "v1.CreateClusterRequest": {
            "type": "object",
            "required": [
                "account_id",
                "kce_cluster_id",
                "name"
            ],
            "properties": {
                "account_id": {
                    "description": "添加 AccountID 字段",
                    "type": "string"
                },
                "ak": {
                    "description": "访问密钥",
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "kce_cluster_id": {
                    "type": "string"
                },
                "log_directory": {
                    "description": "日志存储目录",
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "services": {
                    "description": "需要安装的服务类型列表，如 \"spark\", \"flink\" 等，每个服务可能包含多个安装步骤和工作流",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "sk": {
                    "description": "密钥",
                    "type": "string"
                }
            }
        },
        "v1.CreateClusterResponse": {
            "type": "object",
            "properties": {
                "cluster": {
                    "$ref": "#/definitions/model.Cluster"
                },
                "service_errors": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "services": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "v1.CreateJobRequest": {
            "type": "object",
            "required": [
                "cluster_id",
                "job_type",
                "name"
            ],
            "properties": {
                "account_id": {
                    "description": "从中间件获取，不需要绑定验证",
                    "type": "string"
                },
                "args": {
                    "type": "string"
                },
                "cluster_id": {
                    "type": "string"
                },
                "command": {
                    "type": "string"
                },
                "config": {
                    "type": "object",
                    "additionalProperties": true
                },
                "isSubmit": {
                    "description": "isSubmit: 是否在创建后立即提交，默认 true",
                    "type": "boolean"
                },
                "job_type": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "namespace": {
                    "type": "string"
                }
            }
        },
        "v1.CreateWorkflowRequest": {
            "type": "object",
            "required": [
                "cluster_id",
                "definition",
                "name"
            ],
            "properties": {
                "cluster_id": {
                    "type": "string"
                },
                "definition": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "namespace": {
                    "type": "string"
                }
            }
        },
        "v1.DeleteClusterAsyncRequest": {
            "type": "object",
            "required": [
                "account_id",
                "cluster_id"
            ],
            "properties": {
                "account_id": {
                    "type": "string"
                },
                "cluster_id": {
                    "type": "string"
                }
            }
        },
        "v1.ErrorResponse": {
            "type": "object",
            "properties": {
                "error": {
                    "type": "string"
                }
            }
        },
        "v1.HealthResponse": {
            "type": "object",
            "properties": {
                "service": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "v1.InstallClusterOperatorRequest": {
            "type": "object",
            "required": [
                "operator_type"
            ],
            "properties": {
                "operator_type": {
                    "type": "string"
                }
            }
        },
        "v1.OperatorAsyncRequest": {
            "type": "object",
            "required": [
                "account_id",
                "cluster_id",
                "operator_type"
            ],
            "properties": {
                "account_id": {
                    "type": "string"
                },
                "cluster_id": {
                    "type": "string"
                },
                "operator_type": {
                    "type": "string"
                }
            }
        },
        "v1.Response": {
            "type": "object",
            "properties": {
                "data": {},
                "message": {
                    "type": "string"
                }
            }
        },
        "v1.RetryWorkflowRequest": {
            "type": "object",
            "properties": {
                "step_name": {
                    "type": "string"
                }
            }
        },
        "v1.UpdateClusterInfoRequest": {
            "type": "object",
            "required": [
                "ak",
                "log_directory",
                "sk"
            ],
            "properties": {
                "ak": {
                    "type": "string"
                },
                "log_directory": {
                    "type": "string"
                },
                "sk": {
                    "type": "string"
                }
            }
        },
        "v1.UpdateClusterRequest": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "v1.UpdateJobRequest": {
            "type": "object",
            "required": [
                "status"
            ],
            "properties": {
                "status": {
                    "type": "string"
                }
            }
        },
        "v1.UpdateWorkflowRequest": {
            "type": "object",
            "properties": {
                "definition": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "namespace": {
                    "type": "string"
                }
            }
        },
        "v1.UpdateWorkflowStatusRequest": {
            "type": "object",
            "required": [
                "status"
            ],
            "properties": {
                "status": {
                    "type": "string"
                }
            }
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8080",
	BasePath:         "/api/v1",
	Schemes:          []string{},
	Title:            "Nimbus API",
	Description:      "Nimbus 云平台 PaaS 框架 API 服务",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
