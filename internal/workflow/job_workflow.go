package workflow

// Job 提交相关工作流定义

// CreateSparkJobWorkflow 创建 Spark 任务提交工作流
func (m *ArgoWorkflowManager) CreateSparkJobWorkflow(workflowName, clusterID, jobName, namespace string, jobSpec map[string]interface{}) WorkflowDefinition {
	return WorkflowDefinition{
		Name:      workflowName,
		Namespace: ArgoWorkflowNamespace,
		Labels: map[string]string{
			"app":        "nimbus",
			"job-type":   "spark",
			"cluster-id": clusterID,
			"job-name":   jobName,
		},
		Spec: WorkflowSpec{
			Entrypoint:         "submit-spark-job",
			ServiceAccountName: "kubectl-admin-sa",
			Templates: []WorkflowTemplate{
				{
					Name: "submit-spark-job",
					Steps: [][]WorkflowStep{
						{
							{
								Name:     "submit-spark-application",
								Template: "submit-spark-application-template",
								Arguments: &WorkflowInputs{Parameters: []WorkflowParameter{
									{Name: "cluster-id", Value: clusterID},
									{Name: "job-name", Value: jobName},
									{Name: "namespace", Value: namespace},
									{Name: "job-spec", Value: m.serializeJobSpec(jobSpec)},
								}},
							},
						},
					},
				},
				{
					Name: "submit-spark-application-template",
					Script: &WorkflowScriptSpec{
						Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
						Command: []string{"/bin/sh"},
						Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "开始提交 Spark 任务..."

JOB_NAME="{{inputs.parameters.job-name}}"
NAMESPACE="{{inputs.parameters.namespace}}"

# 任务可选参数
IMAGE="{{inputs.parameters.image}}"
MAIN_CLASS="{{inputs.parameters.main_class}}"
MAIN_APP_FILE="{{inputs.parameters.main_application_file}}"
DRIVER_CORES="{{inputs.parameters.driver_cores}}"
DRIVER_MEMORY="{{inputs.parameters.driver_memory}}"
EXECUTOR_CORES="{{inputs.parameters.executor_cores}}"
EXECUTOR_MEMORY="{{inputs.parameters.executor_memory}}"
EXECUTOR_INSTANCES="{{inputs.parameters.executor_instances}}"

echo "任务名称: $JOB_NAME"
echo "命名空间: $NAMESPACE"

# 根据输入生成 SparkApplication YAML（允许通过 job-spec 传递部分参数）
cat > /tmp/spark-application.yaml <<EOF
apiVersion: "sparkoperator.k8s.io/v1beta2"
kind: SparkApplication
metadata:
  name: $JOB_NAME
  namespace: $NAMESPACE
  labels:
    app: nimbus
    job-type: spark
spec:
  type: Scala
  mode: cluster
  image: "${IMAGE:-hub.kce.ksyun.com/bigdata-platform/pyspark:v3.4.3-ksc1.3}"
  imagePullPolicy: IfNotPresent
  mainClass: ${MAIN_CLASS:-org.apache.spark.examples.SparkPi}
  mainApplicationFile: "${MAIN_APP_FILE:-local:///opt/spark/examples/jars/spark-examples_2.12-3.4.3.jar}"
  sparkVersion: "3.4.3"
  restartPolicy:
    type: Never
  driver:
    cores: ${DRIVER_CORES:-1}
    memory: ${DRIVER_MEMORY:-1g}
    serviceAccount: spark-operator-spark
  executor:
    cores: ${EXECUTOR_CORES:-1}
    memory: ${EXECUTOR_MEMORY:-1g}
    instances: ${EXECUTOR_INSTANCES:-1}
  sparkConf:
    spark.driver.extraJavaOptions: -Duser.timezone=Asia/Shanghai
    spark.executor.extraJavaOptions: -Duser.timezone=Asia/Shanghai
    spark.kubernetes.driver.annotation.prometheus.io/path: /metrics/executors/prometheus/
    spark.kubernetes.driver.annotation.prometheus.io/port: "4040"
    spark.kubernetes.driver.annotation.prometheus.io/scrape: "true"
    spark.kubernetes.driver.reusePersistentVolumeClaim: "false"
    spark.kubernetes.driver.service.deleteOnTermination: "false"
    spark.kubernetes.driver.label.monitored-by: prometheus
	spark.ui.prometheus.enabled: true
	spark.executor.processTreeMetrics.enabled: true
	spark.kubernetes.driver.annotation.prometheus.io/scrape: true
	spark.kubernetes.driver.annotation.prometheus.io/path: /metrics/executors/prometheus/
	spark.kubernetes.driver.annotation.prometheus.io/port: 4040
	spark.metrics.conf.*.sink.prometheusServlet.class: org.apache.spark.metrics.sink.PrometheusServlet
	spark.metrics.conf.*.sink.prometheusServlet.path: /metrics/prometheus
	spark.metrics.conf.master.sink.prometheusServlet.path: /metrics/master/prometheus
	spark.metrics.conf.applications.sink.prometheusServlet.path: /metrics/applications/prometheus
EOF

# 应用 SparkApplication
echo "应用 SparkApplication..."
kubectl apply -f /tmp/spark-application.yaml

# 等待 SparkApplication 创建
echo "等待 SparkApplication 创建..."
kubectl wait --for=condition=available --timeout=60s sparkapplication/$JOB_NAME -n $NAMESPACE || true

# 检查 SparkApplication 状态
echo "检查 SparkApplication 状态..."
kubectl get sparkapplication $JOB_NAME -n $NAMESPACE -o yaml

echo "✓ Spark 任务提交完成"
`,
					},
					Inputs: &WorkflowInputs{Parameters: []WorkflowParameter{
						{Name: "cluster-id"},
						{Name: "job-name"},
						{Name: "namespace"},
						// 任务参数（可选），由服务层传入
						{Name: "image"},
						{Name: "main_class"},
						{Name: "main_application_file"},
						{Name: "driver_cores"},
						{Name: "driver_memory"},
						{Name: "executor_cores"},
						{Name: "executor_memory"},
						{Name: "executor_instances"},
					}},
				},
			},
		},
	}
}

// serializeJobSpec 序列化任务规格为 JSON 字符串
func (m *ArgoWorkflowManager) serializeJobSpec(jobSpec map[string]interface{}) string {
	// 这里应该将 jobSpec 序列化为 JSON 字符串
	// 为了简化，暂时返回空字符串
	return ""
}
