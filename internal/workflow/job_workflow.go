package workflow

import (
	"encoding/json"
	"fmt"
)

// Job 提交相关工作流定义

// CreateSparkJobWorkflow 创建 Spark 任务提交工作流
func (m *ArgoWorkflowManager) CreateSparkJobWorkflow(workflowName, clusterID, jobName, namespace string, jobSpec map[string]interface{}) WorkflowDefinition {
	// 从 jobSpec 提取可选参数，供步骤参数传递（若缺失则传空字符串，模板内有默认值）
	image := getStringFromAny(jobSpec, "image")
	mainClass := firstNonEmpty(
		getStringFromAny(jobSpec, "main_class"),
		getStringFromAny(jobSpec, "mainClass"),
	)
	mainAppFile := firstNonEmpty(
		getStringFromAny(jobSpec, "main_application_file"),
		getStringFromAny(jobSpec, "jarFile"),
		getStringFromAny(jobSpec, "pythonFile"),
	)
	driverCores := firstNonEmpty(getStringFromAny(jobSpec, "driver_cores"), getStringFromAny(jobSpec, "driverCores"))
	driverMemory := firstNonEmpty(getStringFromAny(jobSpec, "driver_memory"), getStringFromAny(jobSpec, "driverMemory"))
	executorCores := firstNonEmpty(getStringFromAny(jobSpec, "executor_cores"), getStringFromAny(jobSpec, "executorCores"))
	executorMemory := firstNonEmpty(getStringFromAny(jobSpec, "executor_memory"), getStringFromAny(jobSpec, "executorMemory"))
	executorInstances := firstNonEmpty(getStringFromAny(jobSpec, "executor_instances"), getStringFromAny(jobSpec, "executorInstances"))

	// 组装步骤参数
	stepParams := []WorkflowParameter{
		{Name: "cluster-id", Value: clusterID},
		{Name: "job-name", Value: jobName},
		{Name: "namespace", Value: namespace},
		{Name: "job-spec", Value: m.serializeJobSpec(jobSpec)},
		{Name: "account_id", Value: getStringFromAny(jobSpec, "accountId")},
		{Name: "nimbus_cluster_id", Value: getStringFromAny(jobSpec, "nimbusClusterId")},
		// 可选参数：全部显式传入，避免 Argo 校验报缺失
		{Name: "image", Value: image},
		{Name: "main_class", Value: mainClass},
		{Name: "main_application_file", Value: mainAppFile},
		{Name: "driver_cores", Value: driverCores},
		{Name: "driver_memory", Value: driverMemory},
		{Name: "executor_cores", Value: executorCores},
		{Name: "executor_memory", Value: executorMemory},
		{Name: "executor_instances", Value: executorInstances},
		// KS3 event log parameters (optional)
		{Name: "spark_event_log_enabled", Value: getStringFromAny(jobSpec, "sparkEventLogEnabled")},
		{Name: "spark_event_log_dir", Value: getStringFromAny(jobSpec, "sparkEventLogDir")},
		{Name: "spark_ks3_endpoint", Value: getStringFromAny(jobSpec, "sparkKs3Endpoint")},
		{Name: "spark_ks3_access_key", Value: getStringFromAny(jobSpec, "sparkKs3AccessKey")},
		{Name: "spark_ks3_access_secret", Value: getStringFromAny(jobSpec, "sparkKs3AccessSecret")},
	}

	return WorkflowDefinition{
		Name:      workflowName,
		Namespace: ArgoWorkflowNamespace,
		Labels: map[string]string{
			"app":        "nimbus",
			"job-type":   "spark",
			"cluster-id": clusterID,
			"job-name":   jobName,
		},
		Spec: WorkflowSpec{
			Entrypoint:         "submit-spark-job",
			ServiceAccountName: "kubectl-admin-sa",
			Templates: []WorkflowTemplate{
				{
					Name: "submit-spark-job",
					Steps: [][]WorkflowStep{
						{
							{
								Name:      "submit-spark-application",
								Template:  "submit-spark-application-template",
								Arguments: &WorkflowInputs{Parameters: stepParams},
							},
						},
					},
				},
				{
					Name: "submit-spark-application-template",
					Script: &WorkflowScriptSpec{
						Image:   "hub.kce.ksyun.com/kmr-on-kce/kubectl:v1.33.3",
						Command: []string{"/bin/sh"},
						Source: kubectlInClusterSetupShell + `
#!/bin/bash
set -e

echo "开始提交 Spark 任务..."

JOB_NAME="{{inputs.parameters.job-name}}"
NAMESPACE="{{inputs.parameters.namespace}}"

# 任务可选参数
IMAGE="{{inputs.parameters.image}}"
MAIN_CLASS="{{inputs.parameters.main_class}}"
MAIN_APP_FILE="{{inputs.parameters.main_application_file}}"
DRIVER_CORES="{{inputs.parameters.driver_cores}}"
DRIVER_MEMORY="{{inputs.parameters.driver_memory}}"
EXECUTOR_CORES="{{inputs.parameters.executor_cores}}"
EXECUTOR_MEMORY="{{inputs.parameters.executor_memory}}"
EXECUTOR_INSTANCES="{{inputs.parameters.executor_instances}}"
ACCOUNT_ID="{{inputs.parameters.account_id}}"
NIMBUS_CLUSTER_ID="{{inputs.parameters.nimbus_cluster_id}}"

echo "任务名称: $JOB_NAME"
echo "命名空间: $NAMESPACE"

# 根据输入生成 SparkApplication YAML（允许通过 job-spec 传递部分参数）
cat > /tmp/spark-application.yaml <<EOF
apiVersion: "sparkoperator.k8s.io/v1beta2"
kind: SparkApplication
metadata:
  name: $JOB_NAME
  namespace: $NAMESPACE
  labels:
    app: nimbus
    job-type: spark
spec:
  type: Scala
  mode: cluster
  image: "${IMAGE:-hub.kce.ksyun.com/bigdata-platform/pyspark:v3.4.3-ksc1.3}"
  imagePullPolicy: IfNotPresent
  mainClass: ${MAIN_CLASS:-org.apache.spark.examples.SparkPi}
  mainApplicationFile: "${MAIN_APP_FILE:-local:///opt/spark/examples/jars/spark-examples_2.12-3.4.3.jar}"
  sparkVersion: "3.4.3"
  # 任务完成后自动清理，单位秒
  timeToLiveSeconds: 60
  restartPolicy:
    type: Never
  driver:
    cores: ${DRIVER_CORES:-1}
    memory: ${DRIVER_MEMORY:-1g}
    serviceAccount: spark-operator-spark
    labels:
      nimbus.account/id: "${ACCOUNT_ID}"
      nimbus.cluster/id: "${NIMBUS_CLUSTER_ID}"
  executor:
    cores: ${EXECUTOR_CORES:-1}
    memory: ${EXECUTOR_MEMORY:-1g}
    instances: ${EXECUTOR_INSTANCES:-1}
    labels:
      nimbus.account/id: "${ACCOUNT_ID}"
      nimbus.cluster/id: "${NIMBUS_CLUSTER_ID}"
  sparkConf:
    spark.driver.extraJavaOptions: -Duser.timezone=Asia/Shanghai
    spark.executor.extraJavaOptions: -Duser.timezone=Asia/Shanghai
    spark.kubernetes.driver.annotation.prometheus.io/path: /metrics/executors/prometheus/
    spark.kubernetes.driver.annotation.prometheus.io/port: "4040"
    spark.kubernetes.driver.annotation.prometheus.io/scrape: "true"
    spark.kubernetes.driver.reusePersistentVolumeClaim: "false"
    spark.kubernetes.driver.service.deleteOnTermination: "true"
    # EventLog to KS3 (if provided)
    spark.eventLog.enabled: "{{inputs.parameters.spark_event_log_enabled}}"
    spark.eventLog.dir: "{{inputs.parameters.spark_event_log_dir}}"
    spark.hadoop.fs.ks3.endpoint: "{{inputs.parameters.spark_ks3_endpoint}}"
    spark.hadoop.fs.ks3.AccessKey: "{{inputs.parameters.spark_ks3_access_key}}"
    spark.hadoop.fs.ks3.AccessSecret: "{{inputs.parameters.spark_ks3_access_secret}}"
  sparkUIOptions:
    servicePort: 4040
    servicePortName: spark-ui-svc
    serviceType: ClusterIP
    ingressAnnotations:
      nginx.ingress.kubernetes.io/rewrite-target: /
    ingressClassName: "kmr-on-kce"
EOF

# 应用 SparkApplication
echo "应用 SparkApplication..."
kubectl apply -f /tmp/spark-application.yaml

# 等待 SparkApplication 创建
echo "等待 SparkApplication 创建..."
kubectl wait --for=condition=available --timeout=60s sparkapplication/$JOB_NAME -n $NAMESPACE || true

# 检查 SparkApplication 状态
echo "检查 SparkApplication 状态..."
kubectl get sparkapplication $JOB_NAME -n $NAMESPACE -o yaml || true

  # 等待 UI Service 准备，并尽早拿到/创建 Ingress
  echo "等待 UI Service 准备..."
  UI_SVC=""
  for i in $(seq 1 60); do
    UI_SVC=$(kubectl get sparkapplication $JOB_NAME -n $NAMESPACE -o jsonpath='{.status.driverInfo.webUIServiceName}' 2>/dev/null || true)
    if [ -n "$UI_SVC" ]; then
      break
    fi
    sleep 5
  done
  echo "webUIServiceName: ${UI_SVC}"

  # 如果已拿到 Service 名称，打印详情并处理 Ingress
  if [ -n "$UI_SVC" ]; then
    # 等待 Service 资源出现
    for i in $(seq 1 20); do
      if kubectl get svc $UI_SVC -n $NAMESPACE >/dev/null 2>&1; then
        break
      fi
      sleep 3
    done

    echo "UI Service ($UI_SVC) 详情:"
    kubectl get svc $UI_SVC -n $NAMESPACE -o yaml || true

    UI_ING_NAME=$(kubectl get sparkapplication $JOB_NAME -n $NAMESPACE -o jsonpath='{.status.driverInfo.webUIIngressName}' 2>/dev/null || true)
    UI_ADDR=$(kubectl get sparkapplication $JOB_NAME -n $NAMESPACE -o jsonpath='{.status.driverInfo.webUIIngressAddress}' 2>/dev/null || true)
    UI_URL=$(kubectl get sparkapplication $JOB_NAME -n $NAMESPACE -o jsonpath='{.status.driverInfo.webUIIngressURL}' 2>/dev/null || true)
    echo "webUIIngressName: ${UI_ING_NAME}"
    echo "webUIIngressAddress: ${UI_ADDR}"
    echo "webUIIngressURL: ${UI_URL}"

  else
    echo "未在预期时间内获取到 UI Service 名称，可能驱动尚未启动。"
  fi

  # 打印 Driver Pod 日志的末尾，便于排查 eventLog 写入问题
  DRIVER_POD=$(kubectl get pods -n $NAMESPACE -l spark-role=driver,sparkoperator.k8s.io/app-name=$JOB_NAME -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || true)
  if [ -n "$DRIVER_POD" ]; then
    echo "Driver Pod: $DRIVER_POD 日志 (末尾200行):"
    kubectl logs -n $NAMESPACE $DRIVER_POD --tail=200 || true
  fi

echo "✓ Spark 任务提交完成"
`,
					},
					Inputs: &WorkflowInputs{Parameters: []WorkflowParameter{
						{Name: "cluster-id"},
						{Name: "job-name"},
						{Name: "namespace"},
						{Name: "job-spec"},
						// 任务参数（可选），提供默认值避免缺失
						{Name: "image", Default: "hub.kce.ksyun.com/bigdata-platform/pyspark:v3.4.3-ksc1.3"},
						{Name: "main_class", Default: "org.apache.spark.examples.SparkPi"},
						{Name: "main_application_file", Default: "local:///opt/spark/examples/jars/spark-examples_2.12-3.4.3.jar"},
						{Name: "driver_cores", Default: "1"},
						{Name: "driver_memory", Default: "1g"},
						{Name: "executor_cores", Default: "1"},
						{Name: "executor_memory", Default: "1g"},
						{Name: "executor_instances", Default: "1"},
						// KS3 event log parameters
						{Name: "spark_event_log_enabled", Default: "false"},
						{Name: "spark_event_log_dir", Default: ""},
						{Name: "spark_ks3_endpoint", Default: ""},
						{Name: "spark_ks3_access_key", Default: ""},
						{Name: "spark_ks3_access_secret", Default: ""},
						// Nimbus labels
						{Name: "account_id", Default: ""},
						{Name: "nimbus_cluster_id", Default: ""},
					}},
				},
			},
		},
	}
}

// serializeJobSpec 序列化任务规格为 JSON 字符串
func (m *ArgoWorkflowManager) serializeJobSpec(jobSpec map[string]interface{}) string {
	if jobSpec == nil {
		return "{}"
	}
	serializedBytes, err := json.Marshal(jobSpec)
	if err != nil {
		return "{}"
	}
	return string(serializedBytes)
}

// firstNonEmpty 返回第一个非空字符串
func firstNonEmpty(values ...string) string {
	for _, v := range values {
		if v != "" {
			return v
		}
	}
	return ""
}

// getStringFromAny 从 map 中获取键对应的字符串表示
func getStringFromAny(m map[string]interface{}, key string) string {
	if m == nil {
		return ""
	}
	val, ok := m[key]
	if !ok || val == nil {
		return ""
	}
	switch v := val.(type) {
	case string:
		return v
	case []byte:
		return string(v)
	case int:
		return fmt.Sprintf("%d", v)
	case int32:
		return fmt.Sprintf("%d", v)
	case int64:
		return fmt.Sprintf("%d", v)
	case float32:
		// 避免小数，如 1.0
		if v == float32(int(v)) {
			return fmt.Sprintf("%d", int(v))
		}
		return fmt.Sprintf("%v", v)
	case float64:
		if v == float64(int(v)) {
			return fmt.Sprintf("%d", int(v))
		}
		return fmt.Sprintf("%v", v)
	case bool:
		if v {
			return "true"
		}
		return "false"
	default:
		return ""
	}
}
