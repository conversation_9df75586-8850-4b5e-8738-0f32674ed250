package api

import (
	"github.com/gin-gonic/gin"
	"github.com/kingsoft/nimbus/docs"
	mw "github.com/kingsoft/nimbus/internal/api/middleware"
	v1 "github.com/kingsoft/nimbus/internal/api/v1"
	"github.com/kingsoft/nimbus/internal/kubernetes"
	"github.com/kingsoft/nimbus/internal/repository"
	"github.com/kingsoft/nimbus/internal/service"
	"github.com/kingsoft/nimbus/internal/workflow"
	"github.com/kingsoft/nimbus/pkg/config"
	"github.com/kingsoft/nimbus/pkg/logger"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// API 结构体包含所有API处理程序
type API struct {
	HealthAPI   *v1.HealthAPI
	ClusterAPI  *v1.ClusterAPI
	JobAPI      *v1.JobAPI
	WorkflowAPI *v1.WorkflowAPI
}

// InitDB 初始化数据库连接
func InitDB(cfg config.DatabaseConfig) error {
	return repository.InitDB(cfg)
}

// SetupRouter 设置路由
func SetupRouter() (*gin.Engine, service.WorkflowService) {
	// 初始化 Swagger 文档
	docs.SwaggerInfo.BasePath = ""
	docs.SwaggerInfo.Title = "Nimbus API"
	docs.SwaggerInfo.Description = "Nimbus 云平台 PaaS 框架 API 服务"
	docs.SwaggerInfo.Version = "1.0"
	docs.SwaggerInfo.Schemes = []string{"http", "https"}

	// 创建 Gin 引擎
	router := gin.New()

	// 使用中间件
	router.Use(gin.Recovery())
	router.Use(mw.Logger())
	router.Use(mw.CORS())

	// 创建 Kubernetes 客户端管理器
	clientManager := kubernetes.NewClientManager()

	// 创建 Operator 工厂
	operatorFactory := kubernetes.NewOperatorFactory(clientManager)

	// 创建仓库
	clusterRepo := repository.NewClusterRepository()
	jobRepo := repository.NewJobRepository()
	workflowRepo := repository.NewWorkflowRepository()
	auditLogRepo := repository.NewAuditLogRepository()

	// 创建 Argo Workflow 管理器
	argoWorkflowManager := workflow.NewArgoWorkflowManager(clientManager)

	// 创建服务
	clusterService := service.NewClusterService(clusterRepo, auditLogRepo, clientManager)
	operatorService := service.NewOperatorService(clusterRepo, auditLogRepo, operatorFactory)
	jobService := service.NewJobService(jobRepo, clusterRepo, auditLogRepo, clientManager, argoWorkflowManager, workflowRepo)
	workflowService := service.NewWorkflowService(workflowRepo, clusterRepo, auditLogRepo, clientManager)

	// 创建 API 处理程序
	healthAPI := v1.NewHealthAPI()
	clusterAPI := v1.NewClusterAPI(clusterService, operatorService, argoWorkflowManager)
	jobAPI := v1.NewJobAPI(jobService)
	workflowAPI := v1.NewWorkflowAPI(workflowService)

	api := &API{
		HealthAPI:   healthAPI,
		ClusterAPI:  clusterAPI,
		JobAPI:      jobAPI,
		WorkflowAPI: workflowAPI,
	}

	// API 版本路由
	v1Router := router.Group("/api/v1")
	{
		// 健康检查路由
		v1Router.GET("/health", api.HealthAPI.Health)

		// 集群路由 - 需要 account_id 验证
		clusters := v1Router.Group("/clusters")
		clusters.Use(mw.SimpleAuth())
		{
			clusters.POST("", api.ClusterAPI.Create)
			clusters.GET("", api.ClusterAPI.List)
			clusters.GET("/:id", api.ClusterAPI.GetByID)
			clusters.PUT("/:id", api.ClusterAPI.Update)
			clusters.PUT("/:id/info", api.ClusterAPI.UpdateClusterInfo)
			clusters.DELETE("/:id", api.ClusterAPI.Delete)
			clusters.POST("/:id/validate", api.ClusterAPI.ValidateConnection)

			// Operator相关路由
			clusters.POST("/:id/operators", api.ClusterAPI.InstallClusterOperator)
			clusters.DELETE("/:id/operators/:operator_type", api.ClusterAPI.UninstallClusterOperator)
			clusters.GET("/:id/operators/:operator_type/status", api.ClusterAPI.GetClusterOperatorStatus)
		}

		// 任务路由 - 需要 account_id 验证
		jobs := v1Router.Group("/jobs")
		jobs.Use(mw.SimpleAuth())
		{
			jobs.POST("", api.JobAPI.Create)
			jobs.GET("", api.JobAPI.List)
			jobs.GET("/:id", api.JobAPI.GetByID)
			jobs.PUT("/:id", api.JobAPI.Update)
			jobs.DELETE("/:id", api.JobAPI.Delete)
			jobs.POST("/:id/submit", api.JobAPI.Submit)
			jobs.POST("/:id/cancel", api.JobAPI.Cancel)
		}

		// 工作流路由 - 需要 account_id 验证
		workflows := v1Router.Group("/workflows")
		workflows.Use(mw.SimpleAuth())
		{
			workflows.POST("", api.WorkflowAPI.Create)
			workflows.GET("", api.WorkflowAPI.List)
			workflows.GET("/:id", api.WorkflowAPI.GetByID)
			workflows.PUT("/:id", api.WorkflowAPI.Update)
			workflows.PUT("/:id/status", api.WorkflowAPI.UpdateStatus)
			workflows.DELETE("/:id", api.WorkflowAPI.Delete)
			workflows.POST("/:id/submit", api.WorkflowAPI.Submit)
			workflows.POST("/:id/cancel", api.WorkflowAPI.Cancel)

			// 异步集群操作
			workflows.POST("/clusters", api.WorkflowAPI.CreateClusterAsync)
			workflows.POST("/clusters/delete", api.WorkflowAPI.DeleteClusterAsync)

			// 异步Operator操作
			workflows.POST("/operators/install", api.WorkflowAPI.InstallOperatorAsync)
			workflows.POST("/operators/uninstall", api.WorkflowAPI.UninstallOperatorAsync)

			// 重试和同步
			workflows.POST("/:id/retry", api.WorkflowAPI.RetryWorkflow)
			workflows.POST("/:id/sync", api.WorkflowAPI.SyncWorkflowStatus)

			// Pod监控和清理
			workflows.POST("/cleanup-pods", api.WorkflowAPI.CleanupCompletedPods)
			workflows.POST("/sync-all", api.WorkflowAPI.SyncAllWorkflowStatuses)
			workflows.POST("/monitor/start", api.WorkflowAPI.StartPodMonitor)
			workflows.POST("/monitor/stop", api.WorkflowAPI.StopPodMonitor)

			// Argo Workflows Web UI
			workflows.GET("/argo-webui-url", api.WorkflowAPI.GetArgoWebUIURL)
		}
	}

	// Swagger 文档路由
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	logger.Info("路由设置完成")
	return router, workflowService
}
