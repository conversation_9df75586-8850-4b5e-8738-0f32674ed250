package middleware

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/kingsoft/nimbus/pkg/logger"
)

// SimpleAuth 简单的认证中间件，从请求体或查询参数中获取 account_id
func SimpleAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		var accountID string

		// 尝试从请求体获取 account_id
		if c.Request.Method == "POST" || c.Request.Method == "PUT" {
			// 读取请求体
			bodyBytes, err := io.ReadAll(c.Request.Body)
			if err == nil {
				// 重新设置请求体，这样后续处理器还能读取
				c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

				// 解析JSON来获取 account_id
				var requestBody map[string]interface{}
				if json.Unmarshal(bodyBytes, &requestBody) == nil {
					if id, exists := requestBody["account_id"]; exists {
						if strID, ok := id.(string); ok && strID != "" {
							accountID = strID
						}
					}
				}
			}
		}

		// 如果请求体中没有，尝试从查询参数获取
		if accountID == "" {
			accountID = c.Query("account_id")
		}

		// 如果都没有，尝试从路径参数获取（用于某些API）
		if accountID == "" {
			accountID = c.Param("account_id")
		}

		// 验证 account_id 是否存在且不为空
		if accountID == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "account_id is required",
			})
			c.Abort()
			return
		}

		logger.Info("SimpleAuth 中间件", "account_id", accountID, "method", c.Request.Method)

		// 将 account_id 设置到上下文中，供后续处理器使用
		c.Set("account_id", accountID)

		c.Next()
	}
}
